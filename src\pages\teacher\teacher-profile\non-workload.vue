<route lang="json5">
{
  style: {
    navigationBarTitleText: '非教学工作量',
  },
}
</route>

<template>
  <view class="container">
    <view class="w-20 ml-4 my-4">
      <SemesterWeekPicker
        v-model:semesterValue="selectedSemester"
        :show-week-label="false"
        :show-week="false"
        :show-all-week="false"
        :show-all-semester="false"
        size="large"
        @semesterChange="handleSemesterChange"
      />
    </view>
    <!-- 统计总览 -->
    <view class="statistics-overview bg-white px-4 py-2 pb-2 mb-3 mx-4 b-rd-2">
      <view class="font-medium mb-4">学期总览</view>
      <view class="flex space-x-3 mb-4">
        <view class="w-1/3 bg-blue-50 rounded-xl p-3 text-center">
          <view class="text-xs text-blue-700 mb-1">总工作量</view>
          <view class="text-xl font-bold text-blue-800">{{ totalWorkload }}</view>
          <view class="text-xs text-blue-600">学时</view>
        </view>
        <view class="w-1/3 bg-green-50 rounded-xl p-3 text-center">
          <view class="text-xs text-green-700 mb-1">已确认</view>
          <view class="text-xl font-bold text-green-800">{{ confirmedWorkload }}</view>
          <view class="text-xs text-green-600">学时</view>
        </view>
        <view class="w-1/3 bg-yellow-50 rounded-xl p-3 text-center">
          <view class="text-xs text-yellow-700 mb-1">审核中</view>
          <view class="text-xl font-bold text-yellow-800">{{ pendingWorkload }}</view>
          <view class="text-xs text-yellow-600">学时</view>
        </view>
      </view>

      <!-- <view class="flex items-center">
        <view class="circle-container relative w-120px h-120px">
          <view
            class="circle-progress"
            :style="`background: conic-gradient(#3b82f6 0% ${completionRate}%, #eef2ff ${completionRate}% 100%)`"
          ></view>
          <view class="circle-inside flex flex-col items-center justify-center">
            <view class="text-2xl font-bold text-blue-600">{{ completionRate }}%</view>
            <view class="text-xs text-gray-500">目标达成率</view>
          </view>
        </view>
        <view class="ml-4 flex-1">
          <view class="flex items-center justify-between mb-1">
            <view class="text-sm text-gray-600">目标工作量</view>
            <view class="text-sm font-medium">{{ targetWorkload }} 学时</view>
          </view>
          <view class="flex items-center justify-between mb-1">
            <view class="text-sm text-gray-600">当前总量</view>
            <view class="text-sm font-medium">{{ totalWorkload }} 学时</view>
          </view>
          <view class="flex items-center justify-between">
            <view class="text-sm text-gray-600">距离目标</view>
            <view class="text-sm font-medium text-blue-600">
              还需 {{ Math.max(0, targetWorkload - totalWorkload) }} 学时
            </view>
          </view>
        </view>
      </view> -->
    </view>

    <!-- 申报记录 -->
    <view class="submission-records bg-white px-4 py-4 mb-3 mx-4 b-rd-2">
      <view class="flex justify-between items-center mb-4">
        <view class="font-medium">申报记录</view>
        <!--        <view class="text-primary text-sm flex items-center">
          新申报
          <wd-icon name="add" size="24rpx" class="ml-1" />
        </view>-->
      </view>

      <!-- 筛选选项卡 -->
      <view class="border-b border-gray-200 mb-3">
        <view class="flex space-x-6">
          <view
            class="text-sm font-medium py-2"
            :class="
              activeTab === 'all' ? 'text-primary border-b-2 border-primary' : 'text-gray-500'
            "
            @click="switchTab('all')"
          >
            全部
          </view>
          <view
            v-for="status in auditStatusOptions"
            :key="status.value"
            class="text-sm font-medium py-2"
            :class="
              activeTab === status.value
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500'
            "
            @click="switchTab(status.value)"
          >
            {{ status.label }}
          </view>
        </view>
      </view>

      <!-- 搜索筛选 -->
      <view class="mb-4">
        <SearchInput
          v-model="searchKeyword"
          placeholder="搜索申报内容关键词"
          :debounce-delay="DEBOUNCE_DELAY"
          @search="searchRecords"
          @clear="clearSearch"
        />
      </view>
    </view>

    <!-- 加载中提示 -->
    <view v-if="loading" class="flex justify-center items-center py-4">
      <wd-loading color="#2563eb" />
      <text class="ml-2 text-gray-600">加载中...</text>
    </view>

    <!-- 记录列表 -->
    <view v-else-if="nonWorkloadItems.length > 0" class="record-list-container w-full box-border">
      <view class="space-y-3 mx-4">
        <!-- 使用API返回的数据动态渲染记录列表 -->
        <view
          v-for="item in nonWorkloadItems"
          :key="item.id"
          class="record-card bg-white rounded-xl shadow-sm border border-gray-100"
        >
          <view class="p-4">
            <view class="flex items-center justify-between mb-2">
              <view
                class="px-3 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(item.shzt)"
              >
                {{ getStatusText(item.shzt) }}
              </view>
              <view class="text-sm text-gray-500">{{ item.gzsj }}</view>
            </view>
            <view class="mb-2">
              <view class="font-medium">{{ item.nr }}</view>
              <view class="text-sm text-gray-500 mt-1">{{ item.xn }}学年 第{{ item.xq }}学期</view>
            </view>
            <view class="flex justify-between items-center">
              <view class="text-sm text-gray-500">
                <wd-icon name="time" size="24rpx" class="mr-1" />
                申报学时: {{ item.gzl }}学时
              </view>
              <view class="text-primary text-sm" @click="viewDetail(item.id)">
                详情
                <wd-icon name="arrow-right" size="24rpx" />
              </view>
            </view>
          </view>
          <view class="px-4 py-2 bg-gray-50 rounded-b-xl" :class="{ 'pb-4': item.shzt === 2 }">
            <view class="flex justify-between mb-1 text-sm">
              <view class="text-gray-500">
                <wd-icon name="scan" size="24rpx" class="mr-1" />
                编号: {{ item.id }}
              </view>
              <!-- 根据审核状态显示不同的操作按钮 -->
            </view>
            <!-- 驳回原因，仅在被驳回状态下显示 -->
            <view v-if="item.shzt === 2 && item.remark" class="text-sm text-red-500">
              <wd-icon name="error" size="24rpx" class="mr-1" />
              驳回原因: {{ item.remark }}
            </view>
          </view>
        </view>

        <!-- 查看更多按钮 -->
        <view class="mt-3 mb-2">
          <Pagination
            :total="totalItems"
            :page="currentPage"
            :pageSize="pageSize"
            @update:page="handlePageChange"
          />
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view v-else class="flex flex-col items-center justify-center py-10">
      <wd-icon name="note" size="80rpx" color="#d1d5db" />
      <view class="text-gray-400 mt-3">暂无申报记录</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { getNonWorkloadList } from '@/service/teacher'
import type { NonWorkloadQuery, NonWorkloadItem } from '@/types/teacher'
import Pagination from '@/components/Pagination/index.vue'
import { loadDictData, getDictLabel, getDictClass, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import SearchInput from '@/components/SearchInput/index.vue'

// 页面数据状态
const loading = ref(false)
const nonWorkloadItems = ref<NonWorkloadItem[]>([])
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 防抖延迟时间
const DEBOUNCE_DELAY = 500 // 防抖延迟时间，单位毫秒

// 学期选择相关
const selectedSemester = ref('')
const currentSemesterLabel = ref('')

// 字典数据
const auditStatusDict = ref<DictData[]>([])
// 审核状态选项
const auditStatusOptions = computed(() => {
  if (!auditStatusDict.value || auditStatusDict.value.length === 0) {
    return [
      { label: '审核中', value: '0' },
      { label: '已通过', value: '1' },
      { label: '被驳回', value: '2' },
    ]
  }
  return getDictOptions(auditStatusDict.value)
})

// 工作量统计数据
const totalWorkload = ref(0) // 总工作量
const confirmedWorkload = ref(0) // 已确认工作量
const pendingWorkload = ref(0) // 审核中工作量
const targetWorkload = ref(210) // 目标工作量

// 计算完成率
const completionRate = computed(() => {
  return Math.round((totalWorkload.value / targetWorkload.value) * 100)
})

// 分类数据
const categoryData = ref([
  { name: '教研科研', hours: 70, percentage: 42.7, color: 'bg-blue-600' },
  { name: '学生竞赛', hours: 45, percentage: 27.4, color: 'bg-green-500' },
  { name: '学生管理', hours: 30, percentage: 18.3, color: 'bg-purple-500' },
  { name: '招生工作', hours: 15, percentage: 9.1, color: 'bg-yellow-500' },
  { name: '社会服务', hours: 4, percentage: 2.5, color: 'bg-red-500' },
])

// 筛选选项
const activeTab = ref('all') // 'all', 'pending', 'approved', 'rejected'
const searchKeyword = ref('')

// 审核状态选项卡映射到字典值
const getAuditStatusValue = (tab: string): string => {
  if (tab === 'all') return ''
  // 使用已有的映射关系
  switch (tab) {
    case 'pending':
      return '0' // 审核中
    case 'approved':
      return '1' // 已通过
    case 'rejected':
      return '2' // 被驳回
    default:
      return tab // 如果直接使用字典值，则原样返回
  }
}

/**
 * 处理学期变更
 * @param semesterInfo 所选学期信息
 */
const handleSemesterChange = (semesterInfo: { label: string; value: string }) => {
  currentSemesterLabel.value = semesterInfo.label
  loadNonWorkloadData()
}

// 获取非教学工作量数据
const loadNonWorkloadData = async () => {
  try {
    loading.value = true

    // 先清空学期总览的统计数据
    totalWorkload.value = 0
    confirmedWorkload.value = 0
    pendingWorkload.value = 0

    // 构建查询参数，符合NonWorkloadQuery类型
    const params: NonWorkloadQuery = {
      page: currentPage.value,
      pageSize: pageSize.value,
    }

    // 处理搜索内容
    if (searchKeyword.value) {
      params.nr = searchKeyword.value // 使用nr字段而不是content
    }

    // 处理审核状态
    if (activeTab.value !== 'all') {
      // 获取审核状态字典值
      params.shzt = getAuditStatusValue(activeTab.value) // 使用shzt字段而不是auditStatus
    }

    // 处理学期参数
    if (selectedSemester.value) {
      // 将学期格式从"2024-2025-1"转换为"2024-2025|1"的格式
      const formattedSemester = selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')
      params.semesters = [formattedSemester]
    }

    console.log('发送搜索请求，参数:', params)
    const res = await getNonWorkloadList(params)

    // 更新记录列表，符合ApiResponse<NonWorkloadResponse>类型
    nonWorkloadItems.value = res.items || []
    totalItems.value = res.total || 0

    // 计算总工作量、已确认工作量和审核中工作量
    if (nonWorkloadItems.value.length > 0) {
      // 遍历所有工作量记录
      nonWorkloadItems.value.forEach((item) => {
        // 将工作量转换为数字，使用gzl字段
        const workload = parseFloat(item.gzl) || 0

        // 累加总工作量
        totalWorkload.value += workload

        // 根据审核状态累加不同类型的工作量，使用shzt字段
        const statusNum = item.shzt
        if (statusNum === 1) {
          confirmedWorkload.value += workload
        } else if (statusNum === 0) {
          pendingWorkload.value += workload
        }
      })

      // 四舍五入统计数据
      totalWorkload.value = Math.round(totalWorkload.value)
      confirmedWorkload.value = Math.round(confirmedWorkload.value)
      pendingWorkload.value = Math.round(pendingWorkload.value)
    }
  } catch (error) {
    console.error('加载非教学工作量数据失败', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 根据审核状态获取状态文本
const getStatusText = (status: number | string): string => {
  // 直接使用字典工具获取状态文本
  const statusStr = String(status)
  console.log(auditStatusDict.value)
  console.log(statusStr)

  return getDictLabel(auditStatusDict.value, statusStr) || '未知状态'
}

// 根据审核状态获取状态样式类
const getStatusClass = (status: number | string): string => {
  // 确保状态值是字符串类型
  const statusStr = String(status)

  // 直接使用字典工具获取样式类
  const dictClass = getDictClass(auditStatusDict.value, statusStr)

  console.log('状态值:', statusStr, '对应样式类:', dictClass)

  // 如果字典中有样式类，则使用字典中的样式，否则使用默认样式
  if (dictClass) {
    // 将字典中的样式类转换为对应的背景和文本颜色类
    switch (dictClass) {
      case 'success':
        return 'bg-green-100 text-green-800'
      case 'primary':
        return 'bg-yellow-100 text-yellow-800'
      case 'danger':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 如果字典中没有样式类，则根据状态值返回默认样式
  switch (statusStr) {
    case '0':
      return 'bg-yellow-100 text-yellow-800' // 待审核
    case '1':
      return 'bg-green-100 text-green-800' // 通过
    case '2':
      return 'bg-red-100 text-red-800' // 不通过
    default:
      return 'bg-gray-100 text-gray-800' // 默认灰色
  }
}

// 查看详情
const viewDetail = (id: number) => {
  uni.showToast({
    title: '查看详情功能待实现',
    icon: 'none',
  })
}

// 撤回申报
const withdrawRecord = (id: number) => {
  uni.showModal({
    title: '确认撤回',
    content: '确定要撤回此申报记录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '撤回功能待实现',
          icon: 'none',
        })
      }
    },
  })
}

// 重新申报
const reapplyRecord = (id: number) => {
  uni.showToast({
    title: '重新申报功能待实现',
    icon: 'none',
  })
}

// 切换选项卡
const switchTab = (tab: string) => {
  activeTab.value = tab
  loadNonWorkloadData()
}

// 清除搜索
const clearSearch = () => {
  // 清除搜索后自动刷新数据
  currentPage.value = 1
  loadNonWorkloadData()
}

// 搜索记录
const searchRecords = () => {
  // 重置页码，确保从第一页开始显示结果
  currentPage.value = 1
  loadNonWorkloadData()
}

// 页码变化处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadNonWorkloadData()
}

// 加载字典数据
const loadDictionaries = async () => {
  try {
    // 加载审核状态字典
    const dicts = await loadDictData(['DM_SPZT2'])
    auditStatusDict.value = dicts.DM_SPZT2
  } catch (error) {
    console.error('加载字典数据失败', error)
  }
}

// 监听学期变化
watch(selectedSemester, (newVal) => {
  if (newVal) {
    loadNonWorkloadData()
  }
})

// 初始化页面数据
onMounted(() => {
  // 先加载字典数据
  loadDictionaries()
    .then(() => {
      // 在SemesterWeekPicker组件初始化时，它会自动设置selectedSemester值
      // 然后通过watch触发数据加载
    })
    .catch((error) => {
      console.error('加载字典数据失败', error)
      // 即使字典加载失败，也继续通过watch触发数据加载
    })
})
</script>

<style lang="scss">
.container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 40rpx;
  overflow-x: hidden;
  background-color: #f5f5f5;
}
/* 添加全局盒模型样式确保元素不会超出容器 */
view,
input {
  box-sizing: border-box;
}

.sticky-top {
  /* position: sticky; */
  top: 0;
  z-index: 10;
  width: 100%;
}

:deep(.picker-container) {
  width: 440rpx;
}

:deep(.semester-select) {
  background-color: #fff !important;
}

.circle-container {
  position: relative;
}

.circle-progress {
  width: 120px;
  height: 120px;
  background: conic-gradient(#3b82f6 0% 78%, #eef2ff 78% 100%);
  border-radius: 50%;
}

.circle-inside {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 100px;
  height: 100px;
  background-color: #fff;
  border-radius: 50%;
}

.record-card {
  box-sizing: border-box;
  width: 100%;
  transition: transform 0.2s;
}

.record-card:active {
  transform: scale(0.99);
}

.search-input {
  box-sizing: border-box;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.submission-records {
  box-sizing: border-box;
}

.record-list-container {
  box-sizing: border-box;
  width: 100%;
}

:deep(.text-primary) {
  color: #2563eb;
}
</style>
