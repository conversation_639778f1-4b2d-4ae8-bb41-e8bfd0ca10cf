<template>
  <view class="section-selector">
    <view
      class="form-input flex items-center justify-between p-[16rpx] rounded-lg bg-gray-50"
      @click="openPopup"
    >
      <text class="text-sm" :class="{ 'text-gray-400': !selectedText }">
        {{ selectedText || placeholder }}
      </text>
      <wd-icon name="arrow-right" custom-style="color: #999; font-size: 28rpx;" />
    </view>

    <!-- 节次选择弹窗 -->
    <wd-popup v-model="showPopup" position="bottom" close-on-click-modal>
      <view class="popup-header">
        <view class="text-gray-500" @click="closePopup">取消</view>
        <view class="popup-title">选择节次</view>
        <view class="text-blue-500" @click="confirmSelection">确定</view>
      </view>
      <view class="section-list">
        <view
          v-for="(option, index) in options"
          :key="option.value"
          class="section-item"
          :class="{
            'section-item-selected': tempSelectedIndex === index,
            'section-item-disabled': option.disabled,
          }"
          @click="selectSection(index)"
        >
          <view class="section-info">
            <text class="section-name">{{ option.name }}</text>
            <text class="section-time">{{ option.times[0] }} - {{ option.times.at(-1) }}</text>
          </view>
          <view v-if="tempSelectedIndex === index" class="check-icon">
            <wd-icon name="check" custom-style="color: #2979ff; font-size: 32rpx;" />
          </view>
          <view v-if="option.disabled" class="disabled-mask">
            <text class="disabled-text">已过时间</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { SectionOption } from '@/types/section'

// Props定义
interface Props {
  /** 当前选中的索引 */
  modelValue?: number
  /** 节次选项数组 */
  options: SectionOption[]
  /** 占位文本 */
  placeholder?: string
  /** 当前选中的值（用于显示） */
  selectedValue?: string
}

// 事件定义
interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: -1,
  placeholder: '请选择节次',
  selectedValue: '',
})

const emit = defineEmits<Emits>()

// 响应式数据
const showPopup = ref(false)
const tempSelectedIndex = ref(-1)

// 计算属性：显示的文本
const selectedText = computed(() => {
  if (props.selectedValue) {
    return props.selectedValue
  }
  if (props.modelValue >= 0 && props.options[props.modelValue]) {
    return props.options[props.modelValue].label
  }
  return ''
})

// 打开弹窗
const openPopup = () => {
  tempSelectedIndex.value = props.modelValue
  showPopup.value = true
}

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false
}

// 选择节次
const selectSection = (index: number) => {
  const option = props.options[index]
  if (option && !option.disabled) {
    tempSelectedIndex.value = index
  }
}

// 确认选择
const confirmSelection = () => {
  if (tempSelectedIndex.value >= 0) {
    emit('update:modelValue', tempSelectedIndex.value)
    emit('change', tempSelectedIndex.value)
  }
  showPopup.value = false
}
</script>

<style lang="scss" scoped>
.section-selector {
  .form-input {
    cursor: pointer;
  }
}

// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-list {
  max-height: 600rpx;
  padding: 0 32rpx 32rpx;
  overflow-y: auto;
}

.section-info {
  flex: 1;

  .section-name {
    display: block;
    margin-bottom: 8rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  .section-time {
    display: block;
    font-size: 24rpx;
    color: #666;
  }
}

.section-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 16rpx;
  margin-bottom: 16rpx;
  cursor: pointer;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.section-item-selected {
    background-color: #e3f2fd;
    border: 2rpx solid #2979ff;
  }

  &:not(.section-item-disabled):hover {
    background-color: #f0f0f0;
  }

  &.section-item-disabled {
    cursor: not-allowed;
    background-color: #f5f5f5;
    opacity: 0.6;

    .section-info {
      color: #999;
    }
  }
}

.check-icon {
  margin-left: 16rpx;
}

.disabled-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;

  .disabled-text {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #999;
    background-color: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
  }
}
</style>
