/**
 * 场地使用申请相关类型定义
 */

/**
 * 空闲教室查询请求参数
 */
export interface FreeRoomsRequest {
  /** 使用日期 YYYY-MM-DD */
  sysj: string
  /** 使用节次 格式：11-12|9-10 */
  syjc: string
  /** 使用场地代码（可选） */
  sycd?: string
}

/**
 * 课程信息
 */
export interface CourseInfo {
  /** 使用班级名称 */
  sybmmc: string
  /** 课程名称 */
  kcmc: string
  /** 使用人姓名 */
  syrxm: string
}

/**
 * 空闲教室信息
 */
export interface FreeRoomInfo {
  /** 使用场地代码 */
  sycddm: string
  /** 使用场地名称 */
  sycdmc: string
  /** 所属校区名称 */
  ssxqmc: string
  /** 所属建筑楼名称 */
  ssjzlmc: string
  /** 班级人数 */
  bjrl: number
  /** 唯一标识 */
  key: string
  /** 是否显示单选按钮 */
  showRadio: boolean
  /** 节次11的课程信息 */
  jc11?: CourseInfo[]
  /** 节次12的课程信息 */
  jc12?: CourseInfo[]
  /** 节次13的课程信息 */
  jc13?: CourseInfo[]
  /** 节次14的课程信息 */
  jc14?: CourseInfo[]
  /** 节次15的课程信息 */
  jc15?: CourseInfo[]
  /** 节次16的课程信息 */
  jc16?: CourseInfo[]
  /** 节次17的课程信息 */
  jc17?: CourseInfo[]
  /** 节次18的课程信息 */
  jc18?: CourseInfo[]
  /** 节次19的课程信息 */
  jc19?: CourseInfo[]
  /** 节次20的课程信息 */
  jc20?: CourseInfo[]
  /** 节次21的课程信息 */
  jc21?: CourseInfo[]
  /** 节次22的课程信息 */
  jc22?: CourseInfo[]
}

/**
 * API响应数据结构
 */
export interface FreeRoomsResponse {
  code: number
  msg: string
  time: number
  data: FreeRoomInfo[]
}

/**
 * 带节次信息的课程信息
 */
export interface CourseInfoWithPeriod extends CourseInfo {
  /** 节次信息，如 '第8节' */
  period: string
}

/**
 * 教室列表显示项
 */
export interface RoomDisplayItem {
  /** 场地代码 */
  sycddm: string
  /** 场地名称 */
  sycdmc: string
  /** 校区名称 */
  ssxqmc: string
  /** 建筑楼名称 */
  ssjzlmc: string
  /** 班级人数 */
  bjrl: number
  /** 是否可用（空闲） */
  available: boolean
  /** 当前节次的课程信息（包含节次） */
  courses: CourseInfoWithPeriod[]
  /** 节次显示信息 */
  periodInfo: {
    /** 日期 */
    date: string
    /** 节次数组，如 ['第8节', '第9节'] */
    periods: string[]
  }
}
