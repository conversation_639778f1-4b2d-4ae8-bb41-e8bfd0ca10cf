<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学工作量',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getTeacherWorkload, getTeacherWorkloadStatistics } from '@/service/teacher'
import type {
  TeacherWorkloadItem,
  TeacherWorkloadQuery,
  SemesterWorkloadStats,
} from '@/types/teacher'
import Pagination from '@/components/Pagination/index.vue'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'

// 加载状态
const loading = ref(false)
const loadError = ref('')

// 当前学期
const selectedSemester = ref('')
const currentSemesterValue = ref('')
const displaySemester = computed(() => {
  if (!selectedSemester.value) return '全部学期'

  const [year, term] = selectedSemester.value.split('-')
  return `${year}-${term}学年第${selectedSemester.value.split('-')[2]}学期`
})

// 判断当前显示的数据是否为当前学年学期的数据
const isCurrentSemesterData = ref(false)

// 工作量数据
const workloadItems = ref<TeacherWorkloadItem[]>([])
const workloadTotal = ref(0)
const workloadData = ref({
  total: 0,
  theory: 0,
  practice: 0,
  other: 0,
})

// 分页参数
const page = ref(1)
const pageSize = ref(100)
const total = ref(0)

// 当前激活的标签页
const activeTab = ref('tab-other')

// 课程工作量数据
const courseWorkloads = ref<any[]>([])

// 历史工作量数据
const historyWorkloads = ref<SemesterWorkloadStats[]>([])
const statisticsLoading = ref(false)
const statisticsError = ref('')

/**
 * 处理学期变更
 * @param semesterInfo 所选学期信息
 */
const handleSemesterChange = (semesterInfo: { label: string; value: string }) => {
  // 更新UI显示
  if (semesterInfo && semesterInfo.label) {
    // 不做任何操作，由watch监听selectedSemester变化触发数据加载
  }
}

// 获取教师工作量数据
const fetchWorkloadData = async () => {
  try {
    loading.value = true
    loadError.value = ''

    // 如果还没有获取当前学期信息，先获取
    if (!currentSemesterValue.value) {
      const currentSemester = await getCurrentSemesterInfo()
      currentSemesterValue.value = currentSemester || ''
    }

    const params: TeacherWorkloadQuery = {
      page: page.value,
      pageSize: pageSize.value,
      semesters: selectedSemester.value
        ? selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')
        : '',
    }

    const res = await getTeacherWorkload(params)
    console.log(res)

    workloadItems.value = res.items
    total.value = res.total

    // 根据返回的数据判断是否为当前学期数据
    if (res.items && res.items.length > 0) {
      const firstItem = res.items[0]
      const dataXn = firstItem.xn // 数据中的学年，如 "2025-2026"
      const dataXq = firstItem.xq // 数据中的学期，如 1
      const dataSemester = `${dataXn}-${dataXq}` // 组合成 "2025-2026-1" 格式

      // 判断数据学期是否与当前学期匹配
      isCurrentSemesterData.value = dataSemester === currentSemesterValue.value
    } else {
      // 如果没有数据，默认不是当前学期数据
      isCurrentSemesterData.value = false
    }

    // 计算工作量统计
    calculateWorkloadStats()

    // 转换数据为课程工作量展示格式
    transformCourseWorkloads()

    loading.value = false
  } catch (error) {
    console.error('获取工作量数据失败:', error)
    loadError.value = '获取数据失败，请稍后重试'
    loading.value = false
  }
}

// 计算工作量统计数据
const calculateWorkloadStats = () => {
  let theory = 0
  let practice = 0
  let other = 0
  let total = 0

  workloadItems.value.forEach((item) => {
    const finalWorkload = parseFloat(item.zzsdgzl)
    total += finalWorkload

    // 根据任务类型判断理论课还是实践课
    // 假设任务类型状态 01 为理论课，02 为实践课，其他为其他工作量
    if (item.rwlxzt === '01') {
      theory += finalWorkload
    } else if (item.rwlxzt === '02') {
      practice += finalWorkload
    } else {
      other += finalWorkload
    }
  })

  workloadData.value = {
    total,
    theory,
    practice,
    other,
  }
}

// 转换数据为课程工作量展示格式
const transformCourseWorkloads = () => {
  // 创建一个对象用于存储合并后的课程工作量
  const mergedCourseMap = new Map<string, any>()

  workloadItems.value.forEach((item) => {
    // 使用课程名称作为键
    const key = item.kcmc

    if (!mergedCourseMap.has(key)) {
      // 如果这个课程还没有记录，初始化一个新的记录
      mergedCourseMap.set(key, {
        id: item.id, // 使用第一个项目的ID
        courseName: item.kcmc,
        taskTypeStatus: item.rwlxzt,
        courseItems: [item], // 保存原始项目列表
        // 初始化合计数据
        totalIdentifyWorkload: parseFloat(item.rdgzl) || 0,
        totalFinalWorkload: parseFloat(item.zzsdgzl) || 0,
        totalAddWorkload: parseFloat(item.hzj) || 0,
        // 保留一些基础信息，用于展示
        // className: item.className, // 移除单一班级名称
        // 收集所有班级名称
        classNames: item.bjmc ? [item.bjmc] : [],
        // 收集所有地点、授课方式和节次
        sites: item.skcdmc ? [item.skcdmc] : [],
        modes: item.skfsmc ? [item.skfsmc] : [],
        sections: item.jcshow ? [item.jcshow] : [],
        // 记录所有授课周次
        cycles: item.zc ? [item.zc] : [],
      })
    } else {
      // 已存在该课程记录，更新数据
      const record = mergedCourseMap.get(key)
      record.courseItems.push(item)

      // 累加工作量
      record.totalIdentifyWorkload += parseFloat(item.rdgzl) || 0
      record.totalFinalWorkload += parseFloat(item.zzsdgzl) || 0
      record.totalAddWorkload += parseFloat(item.hzj) || 0

      // 添加唯一的班级名称
      if (item.bjmc && !record.classNames.includes(item.bjmc)) {
        record.classNames.push(item.bjmc)
      }

      // 添加唯一的地点、授课方式和节次
      if (item.skcdmc && !record.sites.includes(item.skcdmc)) {
        record.sites.push(item.skcdmc)
      }

      if (item.skfsmc && !record.modes.includes(item.skfsmc)) {
        record.modes.push(item.skfsmc)
      }

      if (item.jcshow && !record.sections.includes(item.jcshow)) {
        record.sections.push(item.jcshow)
      }

      // 添加唯一的周次
      if (item.zc && !record.cycles.includes(item.zc)) {
        record.cycles.push(item.zc)
      }
    }
  })

  // 将Map转换为数组
  courseWorkloads.value = Array.from(mergedCourseMap.values())
}

// 切换学期，由于现在使用SemesterWeekPicker，这个方法用于历史记录中的"查看详情"
const changeSemester = (semester: string) => {
  // 直接使用接口返回的学期格式，如 "2023-2024-1"
  selectedSemester.value = semester
  // 切换到工作量详情标签页
  activeTab.value = 'tab-other'
  // 数据加载会由watch监听触发
}

// 切换标签页
const switchTab = (tabId: string) => {
  activeTab.value = tabId
}

// 图表实例引用
const chartRef = ref()

// 获取当前学年学期信息
const getCurrentSemesterInfo = async () => {
  try {
    // 通过学期选择器组件获取当前学期信息
    const res = await import('@/service/semester')
    const semesterRes = await res.getSemesterList()
    const currentSemester = semesterRes.semesters.find((item) => item.isCurrent)
    return currentSemester?.value || null
  } catch (error) {
    console.error('获取当前学期信息失败:', error)
    return null
  }
}

// 获取历史工作量统计数据
const fetchWorkloadStatistics = async () => {
  try {
    statisticsLoading.value = true
    statisticsError.value = ''

    // 获取当前学年学期信息
    const currentSemesterValue = await getCurrentSemesterInfo()
    console.log(currentSemesterValue)

    const res = await getTeacherWorkloadStatistics()

    // 处理历史工作量数据，确保只有真正的当前学期才显示为当前
    historyWorkloads.value = res.semesters
      .map((item) => ({
        ...item,
        // 只有当学期值与真正的当前学期值匹配时，才标记为当前学期
        isCurrent: item.value === currentSemesterValue,
      }))
      .sort((a, b) => {
        // 按学期降序排序，最新的学期在前面
        return b.value.localeCompare(a.value)
      })

    statisticsLoading.value = false

    // 获取数据后初始化图表
    nextTick(() => {
      if (activeTab.value === 'tab-history') {
        initWorkloadChart()
      }
    })
  } catch (error) {
    console.error('获取工作量统计数据失败:', error)
    statisticsError.value = '获取统计数据失败，请稍后重试'
    statisticsLoading.value = false
  }
}

// 初始化工作量趋势图
const initWorkloadChart = async () => {
  if (!chartRef.value || historyWorkloads.value.length === 0) return

  // 准备图表数据
  // 取最近的10个学期，并按照时间顺序（从早到晚）排序
  const recentWorkloads = [...historyWorkloads.value]
    .filter((item) => item.statistics.totalHours) // 过滤掉没有总工作量的学期
    .slice(0, 10)
    .sort((a, b) => a.value.localeCompare(b.value))

  const xAxisData = recentWorkloads.map((item) => item.label)
  const theoryData = recentWorkloads.map((item) => parseFloat(item.statistics.theoryHours || '0'))
  const practiceData = recentWorkloads.map((item) => item.statistics.practiceHours || 0)
  const totalData = recentWorkloads.map((item) => parseFloat(item.statistics.totalHours || '0'))

  // 图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      confine: true,
    },
    legend: {
      data: ['总工作量', '理论教学', '实践教学'],
      bottom: 0,
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 40,
      top: 40,
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
          rotate: 45,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '学时',
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
        },
      },
    ],
    series: [
      {
        name: '总工作量',
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#3b7cff',
        },
        data: totalData,
      },
      {
        name: '理论教学',
        type: 'bar',
        stack: 'workload',
        itemStyle: {
          color: '#52c41a',
        },
        data: theoryData,
      },
      {
        name: '实践教学',
        type: 'bar',
        stack: 'workload',
        itemStyle: {
          color: '#faad14',
        },
        data: practiceData,
      },
    ],
  }

  // 初始化图表
  try {
    const myChart = await chartRef.value.init(echarts)
    myChart.setOption(option)
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 在组件挂载完成后初始化数据和图表
onMounted(async () => {
  // 先获取当前学期信息
  const currentSemester = await getCurrentSemesterInfo()
  currentSemesterValue.value = currentSemester || ''

  // 初始化时加载数据，不传学期参数
  fetchWorkloadData()
  fetchWorkloadStatistics()
})

// 监听学期变化
watch(selectedSemester, (newVal) => {
  if (newVal !== undefined) {
    fetchWorkloadData()
  }
})

// 监听页码变化，重新获取数据
watch(page, () => {
  fetchWorkloadData()
})

// 监听标签页切换，如果切换到历史记录，初始化图表
watch(activeTab, (newVal) => {
  if (newVal === 'tab-history' && historyWorkloads.value.length > 0) {
    nextTick(() => {
      initWorkloadChart()
    })
  }
})
</script>

<template>
  <view class="workload-page px-4 py-4 bg-gray-100">
    <!-- 加载状态 -->
    <view class="loading-container flex justify-center items-center py-10" v-if="loading">
      <wd-loading color="#3b7cff"></wd-loading>
    </view>

    <!-- 错误提示 -->
    <view class="error-tip bg-red-50 text-red-500 p-4 rounded-lg mb-4" v-if="loadError">
      <wd-icon name="error" class="mr-2"></wd-icon>
      {{ loadError }}
    </view>

    <!-- 工作量概览 -->
    <view class="workload-header bg-white rounded-lg px-4 py-2 mb-4 shadow-sm">
      <view class="semester-selector flex justify-between items-center mb-2">
        <view class="w-20">
          <SemesterWeekPicker
            v-model:semesterValue="selectedSemester"
            :show-week-label="false"
            :show-week="false"
            :show-all-week="false"
            :show-all-semester="false"
            size="large"
            @semesterChange="handleSemesterChange"
          />
        </view>
      </view>

      <view class="workload-summary flex gap-3 mb-2">
        <view class="workload-card flex-1 bg-blue-50 rounded-lg p-3 min-w-18">
          <view class="text-sm text-gray-500 mb-2">
            {{ isCurrentSemesterData ? '总工作量' : '总工作量(历史)' }}
          </view>
          <view class="text-xl font-bold text-primary">{{ workloadData.total.toFixed(2) }}</view>
        </view>
        <view class="workload-card flex-1 bg-blue-50 rounded-lg p-3 min-w-18">
          <view class="text-sm text-gray-500 mb-2">
            {{ isCurrentSemesterData ? '理论教学' : '理论教学(历史)' }}
          </view>
          <view class="text-xl font-bold text-primary">{{ workloadData.theory.toFixed(2) }}</view>
        </view>
        <view class="workload-card flex-1 bg-blue-50 rounded-lg p-3 min-w-18">
          <view class="text-sm text-gray-500 mb-2">
            {{ isCurrentSemesterData ? '实践教学' : '实践教学(历史)' }}
          </view>
          <view class="text-xl font-bold text-primary">{{ workloadData.practice.toFixed(2) }}</view>
        </view>
      </view>
    </view>

    <!-- 详细信息选项卡 -->
    <view class="workload-details">
      <view class="detail-tab flex bg-white rounded-lg mb-4 overflow-hidden">
        <view
          class="tab-item flex-1 text-center py-3.5 text-sm font-medium relative"
          :class="{
            'text-primary': activeTab === 'tab-other',
            'text-gray-500': activeTab !== 'tab-other',
          }"
          @click="switchTab('tab-other')"
        >
          工作量详情
          <view
            v-if="activeTab === 'tab-other'"
            class="absolute bottom-0 left-1/4 w-1/2 h-0.75 bg-primary rounded-t-sm"
          ></view>
        </view>
        <view
          class="tab-item flex-1 text-center py-3.5 text-sm font-medium relative"
          :class="{
            'text-primary': activeTab === 'tab-courses',
            'text-gray-500': activeTab !== 'tab-courses',
          }"
          @click="switchTab('tab-courses')"
        >
          课程工作量
          <view
            v-if="activeTab === 'tab-courses'"
            class="absolute bottom-0 left-1/4 w-1/2 h-0.75 bg-primary rounded-t-sm"
          ></view>
        </view>
        <view
          class="tab-item flex-1 text-center py-3.5 text-sm font-medium relative"
          :class="{
            'text-primary': activeTab === 'tab-history',
            'text-gray-500': activeTab !== 'tab-history',
          }"
          @click="switchTab('tab-history')"
        >
          历史记录
          <view
            v-if="activeTab === 'tab-history'"
            class="absolute bottom-0 left-1/4 w-1/2 h-0.75 bg-primary rounded-t-sm"
          ></view>
        </view>
      </view>

      <!-- 工作量详情 -->
      <view v-show="activeTab === 'tab-other'">
        <view
          v-if="workloadItems.length === 0"
          class="bg-white rounded-lg p-4 text-center text-gray-500"
        >
          暂无工作量详情数据
        </view>
        <view
          v-for="item in workloadItems"
          :key="item.id"
          class="bg-white rounded-lg mb-4 overflow-hidden shadow-sm"
        >
          <view class="p-4 border-b border-gray-100">
            <view class="text-lg font-medium mb-2">{{ item.kcmc }}</view>
            <view class="flex justify-between text-sm text-gray-500">
              <view class="flex items-center">
                <wd-icon name="classroom" class="mr-1.5 text-primary" />
                <text>{{ item.bjmc }}</text>
              </view>
              <view class="flex items-center">
                <view
                  class="px-2 py-0.5 rounded-full text-xs font-medium"
                  :class="
                    item.rwlxzt === '01' ? 'bg-blue-50 text-blue-500' : 'bg-green-50 text-green-500'
                  "
                >
                  {{ item.rwlxzt === '01' ? '理论课' : '实践课' }}
                </view>
              </view>
            </view>
          </view>
          <view class="p-4">
            <view class="flex flex-wrap mb-2">
              <view class="w-1/2 mb-2">
                <text class="text-sm text-gray-500 mr-2">认定教师：</text>
                <text class="text-sm">{{ item.rdjsxm }}</text>
              </view>
              <view class="w-1/2 mb-2">
                <text class="text-sm text-gray-500 mr-2">周次：</text>
                <text class="text-sm">{{ item.zc }}</text>
              </view>
              <view class="w-1/2 mb-2" v-if="item.hzj && parseFloat(item.hzj) !== 0">
                <text class="text-sm text-gray-500 mr-2">合作教师：</text>
                <text class="text-sm">{{ item.hzj }}</text>
              </view>
              <view class="w-full mb-2" v-if="item.skrq">
                <text class="text-sm text-gray-500 mr-2">授课日期：</text>
                <text class="text-sm">{{ item.skrq }}</text>
              </view>
              <view class="w-full mb-2" v-if="item.sknl">
                <text class="text-sm text-gray-500 mr-2">授课内容：</text>
                <text class="text-sm">{{ item.sknl }}</text>
              </view>
              <view class="w-full mb-2" v-if="item.remark">
                <text class="text-sm text-gray-500 mr-2">备注：</text>
                <text class="text-sm">{{ item.remark }}</text>
              </view>
            </view>

            <view class="flex gap-3 mt-3">
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">{{ item.rdgzl }}</view>
                <view class="text-xs text-gray-500">认定工作量</view>
              </view>
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">{{ item.rdxs }}</view>
                <view class="text-xs text-gray-500">系数</view>
              </view>
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">{{ item.zzsdgzl }}</view>
                <view class="text-xs text-gray-500">核定工作量</view>
              </view>
            </view>

            <view class="flex flex-wrap gap-2 mt-4">
              <view
                v-if="item.skcdmc"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="location" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ item.skcdmc }}
              </view>
              <view
                v-if="item.skfsmc"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="keywords" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ item.skfsmc }}
              </view>
              <view
                v-if="item.jcshow"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="clock" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ item.jcshow }}
              </view>
            </view>
          </view>
        </view>

        <!-- 分页 -->
        <view class="pagination flex justify-center items-center py-4" v-if="total > pageSize">
          <Pagination v-model:page="page" :total="total" :page-size="pageSize"></Pagination>
        </view>
      </view>

      <!-- 课程工作量 -->
      <view v-show="activeTab === 'tab-courses'">
        <view
          v-if="courseWorkloads.length === 0"
          class="bg-white rounded-lg p-4 text-center text-gray-500"
        >
          暂无课程工作量数据
        </view>
        <view
          v-for="item in courseWorkloads"
          :key="item.id"
          class="course-item bg-white rounded-lg mb-4 overflow-hidden shadow-sm"
        >
          <view class="p-4 border-b border-gray-100 pb-0">
            <view class="text-lg font-medium mb-2">{{ item.courseName }}</view>
            <view class="flex justify-between text-sm text-gray-500">
              <view class="flex items-center">
                <wd-icon name="classroom" class="mr-1.5 text-primary" />
                <text v-if="item.classNames.length === 1">{{ item.classNames[0] }}</text>
                <text v-else>{{ item.classNames.join('、') }}</text>
              </view>
              <view class="flex items-center">
                <view
                  class="px-2 py-0.5 rounded-full text-xs font-medium"
                  :class="
                    item.taskTypeStatus === '01'
                      ? 'bg-blue-50 text-blue-500'
                      : 'bg-green-50 text-green-500'
                  "
                >
                  {{ item.taskTypeStatus === '01' ? '理论课' : '实践课' }}
                </view>
              </view>
            </view>
          </view>

          <view class="p-4 pt-0">
            <!-- 课程基本信息 -->
            <view class="flex flex-wrap mb-2">
              <view class="w-full mb-2" v-if="item.totalAddWorkload && item.totalAddWorkload !== 0">
                <text class="text-sm text-gray-500 mr-2">追加工作量：</text>
                <text class="text-sm">{{ item.totalAddWorkload.toFixed(2) }}</text>
              </view>
            </view>

            <!-- 工作量统计 -->
            <view class="flex gap-3 mt-3">
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">
                  {{ item.totalIdentifyWorkload.toFixed(2) }}
                </view>
                <view class="text-xs text-gray-500">认定工作量</view>
              </view>
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">
                  {{ (item.totalFinalWorkload / item.totalIdentifyWorkload).toFixed(2) }}
                </view>
                <view class="text-xs text-gray-500">平均系数</view>
              </view>
              <view class="flex-1 text-center p-2 bg-gray-100 rounded">
                <view class="text-primary font-semibold mb-1">
                  {{ item.totalFinalWorkload.toFixed(2) }}
                </view>
                <view class="text-xs text-gray-500">核定工作量</view>
              </view>
            </view>

            <!-- 课程标签 -->
            <view class="flex flex-wrap gap-2 mt-4">
              <view
                v-for="(site, sIndex) in item.sites"
                :key="'site-' + sIndex"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="location" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ site }}
              </view>

              <view
                v-for="(mode, mIndex) in item.modes"
                :key="'mode-' + mIndex"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="keywords" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ mode }}
              </view>

              <view
                v-for="(section, secIndex) in item.sections"
                :key="'section-' + secIndex"
                class="px-3 py-1 text-xs bg-gray-100 rounded-full flex items-center"
              >
                <wd-icon name="clock" class="mr-1 text-primary" size="12px"></wd-icon>
                {{ section }}
              </view>
            </view>

            <!-- 展开查看明细按钮 -->
            <view class="mt-4" v-if="item.courseItems.length > 1">
              <wd-button plain block size="small" @click="item.isExpanded = !item.isExpanded">
                {{ item.isExpanded ? '收起明细' : '查看明细(' + item.courseItems.length + '条)' }}
              </wd-button>
            </view>

            <!-- 明细列表 -->
            <view v-if="item.isExpanded" class="mt-4 border-t border-gray-100 pt-4">
              <view
                v-for="(subItem, subIndex) in item.courseItems"
                :key="subItem.id"
                class="bg-gray-50 rounded p-3 mb-3"
              >
                <view class="text-sm font-medium mb-2">明细 #{{ subIndex + 1 }}</view>

                <view class="flex flex-wrap text-xs">
                  <view class="w-1/2 mb-2" v-if="subItem.zc">
                    <text class="text-gray-500 mr-1">周次：</text>
                    <text>{{ subItem.zc }}</text>
                  </view>

                  <view class="w-1/2 mb-2" v-if="subItem.skrq">
                    <text class="text-gray-500 mr-1">授课日期：</text>
                    <text>{{ subItem.skrq }}</text>
                  </view>

                  <view class="w-1/2 mb-2" v-if="subItem.bjmc">
                    <text class="text-gray-500 mr-1">上课班级：</text>
                    <text>{{ subItem.bjmc }}</text>
                  </view>

                  <view class="w-1/2 mb-2" v-if="subItem.jcshow">
                    <text class="text-gray-500 mr-1">节次：</text>
                    <text>{{ subItem.jcshow }}</text>
                  </view>

                  <view class="w-1/2 mb-2" v-if="subItem.skcdmc">
                    <text class="text-gray-500 mr-1">上课地点：</text>
                    <text>{{ subItem.skcdmc }}</text>
                  </view>

                  <view class="w-full mb-2" v-if="subItem.sknl">
                    <text class="text-gray-500 mr-1">授课内容：</text>
                    <text>{{ subItem.sknl }}</text>
                  </view>

                  <view class="w-full mb-2" v-if="subItem.remark">
                    <text class="text-gray-500 mr-1">备注：</text>
                    <text>{{ subItem.remark }}</text>
                  </view>

                  <view class="flex gap-2 w-full mt-2">
                    <view class="flex-1 text-center p-1 bg-white rounded">
                      <view class="text-primary font-medium">{{ subItem.rdgzl }}</view>
                      <view class="text-gray-500 text-2xs">认定</view>
                    </view>

                    <view class="flex-1 text-center p-1 bg-white rounded">
                      <view class="text-primary font-medium">
                        {{ subItem.rdxs }}
                      </view>
                      <view class="text-gray-500 text-2xs">系数</view>
                    </view>

                    <view class="flex-1 text-center p-1 bg-white rounded">
                      <view class="text-primary font-medium">{{ subItem.zzsdgzl }}</view>
                      <view class="text-gray-500 text-2xs">核定</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 分页 -->
        <view class="pagination flex justify-center items-center py-4" v-if="total > pageSize">
          <Pagination v-model:page="page" :total="total" :page-size="pageSize"></Pagination>
        </view>
      </view>

      <!-- 历史记录 -->
      <view v-show="activeTab === 'tab-history'">
        <!-- 加载状态 -->
        <view
          class="loading-container flex justify-center items-center py-10"
          v-if="statisticsLoading"
        >
          <wd-loading color="#3b7cff"></wd-loading>
        </view>

        <!-- 错误提示 -->
        <view class="error-tip bg-red-50 text-red-500 p-4 rounded-lg mb-4" v-if="statisticsError">
          <wd-icon name="error" class="mr-2"></wd-icon>
          {{ statisticsError }}
        </view>

        <!-- 空数据提示 -->
        <view
          v-if="!statisticsLoading && !statisticsError && historyWorkloads.length === 0"
          class="bg-white rounded-lg p-4 text-center text-gray-500"
        >
          暂无工作量历史数据
        </view>

        <view
          v-for="(item, index) in historyWorkloads"
          :key="index"
          class="bg-white rounded-lg mb-4 overflow-hidden shadow-sm"
        >
          <view class="p-4 border-b border-gray-100">
            <view class="flex justify-between">
              <view class="text-lg font-medium">{{ item.label.replace(/-/g, '-') }}学期</view>
              <view
                v-if="item.seasonal"
                class="px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-500"
              >
                {{ item.seasonal }}
              </view>
              <view
                v-if="item.isCurrent"
                class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-500"
              >
                当前学期
              </view>
            </view>
          </view>
          <view class="p-4">
            <view class="flex mb-2">
              <view class="w-20 text-sm text-gray-500">总工作量：</view>
              <view class="flex-1 text-sm">{{ item.statistics.totalHours || '0' }}学时</view>
            </view>
            <view class="flex mb-2">
              <view class="w-20 text-sm text-gray-500">理论教学：</view>
              <view class="flex-1 text-sm">{{ item.statistics.theoryHours || '0' }}学时</view>
            </view>
            <view class="flex mb-2">
              <view class="w-20 text-sm text-gray-500">实践教学：</view>
              <view class="flex-1 text-sm">{{ item.statistics.practiceHours || '0' }}学时</view>
            </view>

            <!-- 查看详情按钮 -->
            <wd-button plain block @click="changeSemester(item.value)">查看详情</wd-button>
          </view>
        </view>

        <!-- 工作量趋势图 -->
        <view v-if="historyWorkloads.length > 0" class="chart-container mt-4 p-4">
          <view class="text-base font-medium mb-4">工作量趋势</view>
          <view class="chart-area">
            <l-echart ref="chartRef"></l-echart>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.workload-page {
  min-height: 100vh;
  --primary-color: #3b7cff;
  --primary-color-rgb: 59, 124, 255;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --secondary-color: #7e3ff2;
  --danger-color: #f5222d;
  --light-gray: #f0f2f5;
  --secondary-text-color: #666666;
  --border-radius: 8rpx;
}

.chart-container {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-area {
  height: 600rpx;
}

.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-blue-50 {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

:deep(.picker-container) {
  width: 440rpx;
}
:deep(.semester-select) {
  background-color: #fff !important;
}

.text-2xs {
  font-size: 0.65rem;
}
</style>
