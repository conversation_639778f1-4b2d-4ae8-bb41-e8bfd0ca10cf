/**
 * 考勤查询参数
 */
export interface AttendanceQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 学生姓名 */
  studentName?: string
  /** 课程名称 */
  courseName?: string
  /** 教师姓名 */
  teacherName?: string
  /** 授课日期 */
  teachingDate?: string
  /** 上课节次 */
  classPeriod?: string
  /** 考勤状态列表 */
  attendanceStatus?: string[]
  attendanceStatusName?: string
  /** 课时 */
  classHours?: string
  /** 提交人姓名 */
  submitterName?: string
  /** 考勤时间 */
  attendanceTime?: string
  /** 备注 */
  remark?: string
  /** 学期列表 */
  semesters: string[]
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: string
}

/**
 * 考勤记录项
 */
export interface AttendanceItem {
  /** 考勤ID */
  attendanceId: number
  /** 考勤数据类型 */
  attendanceDataType: number
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 教学任务ID */
  teachingTaskId: number
  /** 课程名称 */
  courseName: string
  /** 周数 */
  weekNumber: number
  /** 授课日期 */
  teachingDate: string
  /** 考勤开始时间 */
  attendanceStartTime: string
  /** 考勤结束时间 */
  attendanceEndTime: string
  /** 课节 */
  classPeriod: string
  /** 上课计划ID */
  skjhid: number
  /** 课时 */
  classHours: number
  /** 学院代码 */
  collegeCode: string
  /** 系部代码 */
  departmentCode: string
  /** 系部名称 */
  departmentName: string
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 学生代码 */
  studentCode: string
  /** 座位号 */
  seatNumber: number
  /** 学生姓名 */
  studentName: string
  /** 考勤状态 */
  attendanceStatus: string
  attendanceStatusName: string
  /** 考勤签退状态 */
  attendanceCheckOutStatus: number
  /** 提交人代码 */
  submitterCode: string
  /** 提交人姓名 */
  submitterName: string
  /** 考勤时间 */
  attendanceTime: string
  /** 考勤签退时间 */
  attendanceCheckOutTime: string
  /** 相关请假ID */
  relatedLeaveId: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 临时上报 */
  lysb: number
  /** 是否及时考勤 */
  sfjcks: number
  /** 教师姓名 */
  teacherName: string
  /** 状态颜色 */
  statusColor: string
}

/**
 * 考勤数据响应
 */
export interface AttendanceResponse {
  /** 数据项列表 */
  items: AttendanceItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 考勤统计
 */
export interface AttendanceStatistics {
  /** 总课程数 */
  totalCourses: number
  /** 正常出勤数 */
  normalAttendance: number
  /** 请假数量 */
  leaveCount: number
  /** 旷课数量 */
  absentCount: number
  /** 出勤率 */
  attendanceRate: number
  /** 迟到率 */
  lateRate: number
}

/**
 * 课程考勤
 */
export interface CourseAttendance {
  /** ID */
  id: number
  /** 课程名称 */
  courseName: string
  /** 教师姓名 */
  teacherName: string
  /** 课程时间 */
  courseTime: string
  /** 已上课时 */
  finishedLessons: number
  /** 总课时 */
  totalLessons: number
  /** 出勤率 */
  attendanceRate: number
  /** 图标类型 */
  iconType: string
}

/**
 * 班级排名
 */
export interface ClassRanking {
  /** ID */
  id: number
  /** 排名 */
  rank: number
  /** 姓名 */
  name: string
  /** 出勤率 */
  attendanceRate: number
  /** 是否当前用户 */
  isCurrentUser: boolean
}
