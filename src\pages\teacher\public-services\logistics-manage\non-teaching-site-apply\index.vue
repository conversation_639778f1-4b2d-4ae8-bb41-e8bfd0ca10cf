<route lang="json5">
{
  style: {
    navigationBarTitleText: '非教学场地使用申请',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ApplyForm from './components/ApplyForm.vue'
import StatusTag, { type StatusType } from '@/components/common/StatusTag.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import { getSiteUseApplyDetailList } from '@/service/venue'
import type { SiteUseApplyDetail } from '@/types/venue'

// 申请表单组件引用
const applyFormRef = ref()

// 已选申请场地列表
const selectedSiteList = ref<SiteUseApplyDetail[]>([])
const loading = ref(false)

// 获取表单数据的方法（供后续使用）
const getFormData = () => {
  return applyFormRef.value?.formData
}

// 获取已选申请场地列表
const getSelectedSiteList = async () => {
  try {
    loading.value = true
    const res = await getSiteUseApplyDetailList({
      page: 1,
      pageSize: 10000,
      pid: 15,
      spzt: 0,
    })
    selectedSiteList.value = res.items || []
  } catch (error) {
    console.error('获取已选申请场地列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 获取星期显示文本
const getWeekText = (xqs: number) => {
  const weekMap: Record<number, string> = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日',
  }
  return weekMap[xqs] || ''
}

// 获取状态显示文本
const getStatusText = (spzt: number) => {
  const statusMap: Record<number, string> = {
    0: '待审批',
    1: '已通过',
    2: '已拒绝',
  }
  return statusMap[spzt] || '未知'
}

// 获取状态类型
const getStatusType = (spzt: number): StatusType => {
  const typeMap: Record<number, StatusType> = {
    0: 'warning',
    1: 'primary',
    2: 'danger',
  }
  return typeMap[spzt] || 'default'
}

// 跳转到申请表单页面
const goToApplyForm = () => {
  uni.navigateTo({
    url: '/pages/teacher/public-services/logistics-manage/non-teaching-site-apply/apply-form/index',
  })
}

// 页面初始化
onMounted(() => {
  getSelectedSiteList()
})
</script>

<template>
  <view class="non-teaching-site-apply-page">
    <!-- 申请表单 -->
    <ApplyForm ref="applyFormRef" />

    <!-- 添加场地按钮 -->
    <view class="bg-white p-2 rounded-lg mb-[24rpx]">
      <ActionButton type="primary" text="添加场地" @click="goToApplyForm" class="add-site-button" />
    </view>

    <!-- 已选申请场地列表 -->
    <view class="selected-site-list">
      <view class="list-header">
        <text class="header-title">已选申请场地列表</text>
        <text class="header-count">({{ selectedSiteList.length }})</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-load-more status="loading" />
      </view>

      <!-- 列表内容 -->
      <view v-else-if="selectedSiteList.length > 0" class="list-content">
        <view v-for="(item, index) in selectedSiteList" :key="item.id" class="site-card">
          <view class="card-header">
            <text class="card-index">{{ index + 1 }}</text>
            <view class="card-status">
              <StatusTag
                :text="getStatusText(item.spzt)"
                :type="getStatusType(item.spzt)"
                size="small"
              />
            </view>
          </view>

          <view class="card-content">
            <view class="content-row">
              <text class="label">使用主题：</text>
              <text class="value">{{ item.sknl || '-' }}</text>
            </view>
            <view class="content-row">
              <text class="label">使用日期：</text>
              <text class="value">{{ item.skrq }} {{ getWeekText(item.xqs) }}</text>
            </view>
            <view class="content-row">
              <text class="label">使用节次：</text>
              <text class="value">第{{ item.jcshow }}节</text>
            </view>
            <view class="content-row">
              <text class="label">负责人：</text>
              <text class="value">{{ item.skjsxm || '-' }}</text>
            </view>
            <view class="content-row">
              <text class="label">使用场地：</text>
              <text class="value">{{ item.skcdmc || '-' }}</text>
            </view>
            <view class="content-row">
              <text class="label">备注：</text>
              <text class="value">{{ item.remark || '-' }}</text>
            </view>
          </view>

          <view class="card-actions">
            <uni-button size="mini" type="primary">查看详情</uni-button>
            <uni-button size="mini" type="warn">编辑</uni-button>
            <uni-button size="mini" type="error">删除</uni-button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无申请记录</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.non-teaching-site-apply-page {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f5f5f5;
}

.selected-site-list {
  .list-header {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-bottom: 2rpx solid #f0f0f0;
    border-radius: 16rpx 16rpx 0 0;

    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .header-count {
      margin-left: 10rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .loading-container {
    padding: 60rpx 0;
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;
  }

  .list-content {
    overflow: hidden;
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;
  }

  .site-card {
    padding: 30rpx;
    border-bottom: 2rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .card-index {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        font-size: 24rpx;
        font-weight: 600;
        color: #fff;
        background-color: #007aff;
        border-radius: 50%;
      }
    }

    .card-content {
      margin-bottom: 24rpx;

      .content-row {
        display: flex;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          flex-shrink: 0;
          width: 160rpx;
          font-size: 28rpx;
          color: #666;
        }

        .value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          word-break: break-all;
        }
      }
    }

    .card-actions {
      display: flex;
      gap: 20rpx;
      justify-content: flex-end;
    }
  }

  .empty-state {
    padding: 100rpx 0;
    text-align: center;
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}
</style>
